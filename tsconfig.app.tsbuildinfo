{"root": ["./src/main.ts", "./src/vite-env.d.ts", "./src/composables/usepermission.ts", "./src/router/index.ts", "./src/services/adjustdetail.ts", "./src/services/api.ts", "./src/services/auth.ts", "./src/services/bankstatement.ts", "./src/services/chargedetail.ts", "./src/services/contract.ts", "./src/services/customer.ts", "./src/services/department.ts", "./src/services/feepackage.ts", "./src/services/feetemplate.ts", "./src/services/invoice.ts", "./src/services/menu.ts", "./src/services/public.ts", "./src/services/role.ts", "./src/services/router.ts", "./src/services/toast.ts", "./src/services/user.ts", "./src/services/income/order.ts", "./src/stores/user.ts", "./src/types/adjustdetail.ts", "./src/types/api.ts", "./src/types/bankstatement.ts", "./src/types/chargedetail.ts", "./src/types/contract.ts", "./src/types/customer.ts", "./src/types/department.ts", "./src/types/feepackage.ts", "./src/types/feetemplate.ts", "./src/types/invoice.ts", "./src/types/menu.ts", "./src/types/order.ts", "./src/types/public.ts", "./src/types/role.ts", "./src/types/user.ts", "./src/utils/basic.ts", "./src/utils/common.ts", "./src/utils/const.ts", "./src/utils/options.ts", "./src/app.vue", "./src/components/footer.vue", "./src/components/helloworld.vue", "./src/views/dashboard.vue", "./src/views/forgotpassword.vue", "./src/views/login.vue", "./src/views/account/adjust.vue", "./src/views/account/adjustaccountdrawer.vue", "./src/views/account/chargedetaildialog.vue", "./src/views/account/chargedetaillist.vue", "./src/views/account/incomeadjustdrawer.vue", "./src/views/contract/contractdetail.vue", "./src/views/contract/contractlist.vue", "./src/views/contract/invoiceinfo.vue", "./src/views/customer/accountseqlist.vue", "./src/views/customer/approvehistory.vue", "./src/views/customer/customerapproval.vue", "./src/views/customer/customerdetail.vue", "./src/views/customer/customerlist.vue", "./src/views/income/billingprocess.vue", "./src/views/income/feepackagelist.vue", "./src/views/income/feetemplatelist.vue", "./src/views/income/orderdetail.vue", "./src/views/income/orderlist.vue", "./src/views/income/orderprocess.vue", "./src/views/menu/menulist.vue", "./src/views/receipt/recognitiondetail.vue", "./src/views/receipt/bankflow.vue", "./src/views/user/departmentlist.vue", "./src/views/user/rolelist.vue"], "version": "5.8.3"}